import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { CreateCompanyDto } from '../../../../core/models/create-company.dto';

@Component({
  selector: 'app-create-company',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './create-company.component.html',
  styleUrls: ['./create-company.component.scss']
})
export class CreateCompanyComponent implements OnInit {
  companyForm: FormGroup;
  isSubmitting = false;

  // Mock data for dropdowns
  countries = [
    { label: 'United States', value: 'US' },
    { label: 'Canada', value: 'CA' },
    { label: 'United Kingdom', value: 'UK' },
    { label: 'Germany', value: 'DE' },
    { label: 'France', value: 'FR' },
    { label: 'India', value: 'IN' },
    { label: 'Australia', value: 'AU' }
  ];

  states = [
    { label: 'California', value: 'CA' },
    { label: 'New York', value: 'NY' },
    { label: 'Texas', value: 'TX' },
    { label: 'Florida', value: 'FL' },
    { label: 'Illinois', value: 'IL' },
    { label: 'Pennsylvania', value: 'PA' },
    { label: 'Ohio', value: 'OH' }
  ];

  constructor(private fb: FormBuilder) {
    this.companyForm = this.createForm();
  }

  ngOnInit(): void {
    // Initialize form with mock product owner ID
    this.companyForm.patchValue({
      product_owner_id: '123e4567-e89b-12d3-a456-************' // Mock UUID
    });
  }

  private createForm(): FormGroup {
    return this.fb.group({
      name: ['', [Validators.required, Validators.minLength(2)]],
      product_owner_id: ['', [Validators.required]],
      contact_person_firstName: ['', [Validators.required, Validators.minLength(2)]],
      contact_person_lastName: ['', [Validators.required, Validators.minLength(2)]],
      contact_person_email: ['', [Validators.required, Validators.email]],
      contact_person_phone: [''],
      address_line: ['', [Validators.required]],
      zipcode: ['', [Validators.required, Validators.pattern(/^\d{5}(-\d{4})?$/)]],
      country: ['', [Validators.required]],
      state: ['', [Validators.required]],
      city: ['', [Validators.required]]
    });
  }

  onSubmit(): void {
    if (this.companyForm.valid) {
      this.isSubmitting = true;
      
      const formData: CreateCompanyDto = this.companyForm.value;
      
      // Log the form data (mock API call)
      console.log('=== CREATE COMPANY FORM DATA ===');
      console.log(JSON.stringify(formData, null, 2));
      
      // Simulate API call delay
      setTimeout(() => {
        this.isSubmitting = false;
        alert('Company created successfully! Check console for form data.');
        this.resetForm();
      }, 1500);
    } else {
      this.markFormGroupTouched();
    }
  }

  resetForm(): void {
    this.companyForm.reset();
    this.companyForm.patchValue({
      product_owner_id: '123e4567-e89b-12d3-a456-************' // Keep mock UUID
    });
  }

  private markFormGroupTouched(): void {
    Object.keys(this.companyForm.controls).forEach(key => {
      const control = this.companyForm.get(key);
      control?.markAsTouched();
    });
  }

  // Helper methods for template
  isFieldInvalid(fieldName: string): boolean {
    const field = this.companyForm.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  getFieldError(fieldName: string): string {
    const field = this.companyForm.get(fieldName);
    if (field?.errors) {
      if (field.errors['required']) return `${fieldName.replace('_', ' ')} is required`;
      if (field.errors['email']) return 'Please enter a valid email address';
      if (field.errors['minlength']) return `Minimum ${field.errors['minlength'].requiredLength} characters required`;
      if (field.errors['pattern']) return 'Please enter a valid format';
    }
    return '';
  }
}
