<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h5 class="mb-0">{{ isEditMode ? 'Edit Question' : 'Create New Question' }}</h5>
        </div>
        <div class="card-body">
          <div *ngIf="loading" class="text-center">
            <div class="spinner-border" role="status">
              <span class="sr-only">Loading...</span>
            </div>
          </div>

          <div *ngIf="error" class="alert alert-danger">{{ error }}</div>

          <form  [formGroup]="questionForm" (ngSubmit)="onSubmit()" *ngIf="!loading">
            <div class="form-group mb-3">
              <label for="question_text" class="form-label">Question Text</label>
              <textarea 
                id="question_text"
                formControlName="question_text"
                class="form-control"
                [class.is-invalid]="questionForm.get('question_text')?.touched && questionForm.get('question_text')?.errors"
                rows="3"
                placeholder="Enter your question here...">
              </textarea>
              <div *ngIf="questionForm.get('question_text')?.touched && questionForm.get('question_text')?.errors" class="invalid-feedback">
                <div *ngIf="questionForm.get('question_text')?.errors?.['required']">Question text is required</div>
                <div *ngIf="questionForm.get('question_text')?.errors?.['minlength']">Question text must be at least 5 characters long</div>
              </div>
            </div>

            <div class="form-group mb-3">
              <label class="form-label">Answer Options</label>
              <div formArrayName="options">
                <div *ngFor="let option of options.controls; let i = index" [formGroupName]="i" class="mb-3">
                  <div class="row mb-2">
                    <div class="col-md-4">
                      <label class="form-label">Option Text</label>
                      <input
                        type="text"
                        formControlName="option_text"
                        class="form-control"
                        [class.is-invalid]="option.get('option_text')?.touched && option.get('option_text')?.errors"
                        placeholder="Option text">
                      <div *ngIf="option.get('option_text')?.touched && option.get('option_text')?.errors" class="invalid-feedback">
                        Option text is required
                      </div>
                    </div>
                    <div class="col-md-3">
                      <label class="form-label">Score Value</label>
                      <input
                        type="number"
                        formControlName="option_value"
                        class="form-control"
                        [class.is-invalid]="option.get('option_value')?.touched && option.get('option_value')?.errors"
                        placeholder="Score value"
                        min="0">
                      <div *ngIf="option.get('option_value')?.touched && option.get('option_value')?.errors" class="invalid-feedback">
                        <div *ngIf="option.get('option_value')?.errors?.['required']">Score is required</div>
                        <div *ngIf="option.get('option_value')?.errors?.['min']">Score must be 0 or greater</div>
                      </div>
                    </div>
                    <div class="col-md-3">
                      <label class="form-label">Category</label>
                      <select
                        formControlName="categoryId"
                        class="form-control">
                        <option value="">Select Category (Optional)</option>
                        <option *ngFor="let category of categories" [value]="category.id">
                          {{category.name}}
                        </option>
                      </select>
                      <div *ngIf="loadingCategories" class="text-muted small mt-1">
                        Loading categories...
                      </div>
                    </div>
                    <div class="col-md-2">
                      <label class="form-label">&nbsp;</label>
                      <div>
                        <button
                          type="button"
                          class="btn btn-outline-danger"
                          (click)="removeOption(i)"
                          [disabled]="options.length <= 2">
                          Remove
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <button type="button" class="btn btn-outline-primary mt-2" (click)="addOption()">
                Add Option
              </button>
            </div>

            <div class="form-group">
              <button 
                type="submit" 
                class="btn btn-primary me-2"
                [disabled]="questionForm.invalid || submitting">
                <span *ngIf="submitting" class="spinner-border spinner-border-sm me-1"></span>
                {{ isEditMode ? 'Update Question' : 'Create Question' }}
              </button>
              <button 
                type="button" 
                class="btn btn-secondary"
                (click)="router.navigate(['/questions'])">
                Cancel
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
