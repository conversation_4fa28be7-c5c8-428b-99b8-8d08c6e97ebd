<div class="create-company-container">
  <div class="company-form-card">
    <div class="card-header">
      <h2>Create New Company</h2>
    </div>
    <div class="card-body">
      <form [formGroup]="companyForm" (ngSubmit)="onSubmit()" class="company-form">
      
      <!-- Company Information Section -->
      <div class="form-section">
        <h3 class="section-title">Company Information</h3>
        
        <div class="form-row">
          <div class="form-field">
            <label for="name" class="field-label">Company Name *</label>
            <input
              id="name"
              type="text"
              class="form-control"
              formControlName="name"
              placeholder="Enter company name"
              [class.is-invalid]="isFieldInvalid('name')"
            />
            <small class="error-message" *ngIf="isFieldInvalid('name')">
              {{ getFieldError('name') }}
            </small>
          </div>
        </div>
      </div>

      <!-- Contact Person Section -->
      <div class="form-section">
        <h3 class="section-title">Contact Person</h3>
        
        <div class="form-row">
          <div class="form-field">
            <label for="firstName" class="field-label">First Name *</label>
            <input
              id="firstName"
              type="text"
              class="form-control"
              formControlName="contact_person_firstName"
              placeholder="Enter first name"
              [class.is-invalid]="isFieldInvalid('contact_person_firstName')"
            />
            <small class="error-message" *ngIf="isFieldInvalid('contact_person_firstName')">
              {{ getFieldError('contact_person_firstName') }}
            </small>
          </div>

          <div class="form-field">
            <label for="lastName" class="field-label">Last Name *</label>
            <input
              id="lastName"
              type="text"
              class="form-control"
              formControlName="contact_person_lastName"
              placeholder="Enter last name"
              [class.is-invalid]="isFieldInvalid('contact_person_lastName')"
            />
            <small class="error-message" *ngIf="isFieldInvalid('contact_person_lastName')">
              {{ getFieldError('contact_person_lastName') }}
            </small>
          </div>
        </div>

        <div class="form-row">
          <div class="form-field">
            <label for="email" class="field-label">Email *</label>
            <input
              id="email"
              type="email"
              class="form-control"
              formControlName="contact_person_email"
              placeholder="Enter email address"
              [class.is-invalid]="isFieldInvalid('contact_person_email')"
            />
            <small class="error-message" *ngIf="isFieldInvalid('contact_person_email')">
              {{ getFieldError('contact_person_email') }}
            </small>
          </div>

          <div class="form-field">
            <label for="phone" class="field-label">Phone</label>
            <input
              id="phone"
              type="tel"
              class="form-control"
              formControlName="contact_person_phone"
              placeholder="Enter phone number (optional)"
            />
          </div>
        </div>
      </div>

      <!-- Address Section -->
      <div class="form-section">
        <h3 class="section-title">Address Information</h3>
        
        <div class="form-row">
          <div class="form-field full-width">
            <label for="address" class="field-label">Address Line *</label>
            <input
              id="address"
              type="text"
              class="form-control"
              formControlName="address_line"
              placeholder="Enter street address"
              [class.is-invalid]="isFieldInvalid('address_line')"
            />
            <small class="error-message" *ngIf="isFieldInvalid('address_line')">
              {{ getFieldError('address_line') }}
            </small>
          </div>
        </div>

        <div class="form-row">
          <div class="form-field">
            <label for="country" class="field-label">Country *</label>
            <select
              id="country"
              class="form-control"
              formControlName="country"
              [class.is-invalid]="isFieldInvalid('country')"
            >
              <option value="">Select country</option>
              <option *ngFor="let country of countries" [value]="country.value">
                {{ country.label }}
              </option>
            </select>
            <small class="error-message" *ngIf="isFieldInvalid('country')">
              {{ getFieldError('country') }}
            </small>
          </div>

          <div class="form-field">
            <label for="state" class="field-label">State *</label>
            <select
              id="state"
              class="form-control"
              formControlName="state"
              [class.is-invalid]="isFieldInvalid('state')"
            >
              <option value="">Select state</option>
              <option *ngFor="let state of states" [value]="state.value">
                {{ state.label }}
              </option>
            </select>
            <small class="error-message" *ngIf="isFieldInvalid('state')">
              {{ getFieldError('state') }}
            </small>
          </div>
        </div>

        <div class="form-row">
          <div class="form-field">
            <label for="city" class="field-label">City *</label>
            <input
              id="city"
              type="text"
              class="form-control"
              formControlName="city"
              placeholder="Enter city"
              [class.is-invalid]="isFieldInvalid('city')"
            />
            <small class="error-message" *ngIf="isFieldInvalid('city')">
              {{ getFieldError('city') }}
            </small>
          </div>

          <div class="form-field">
            <label for="zipcode" class="field-label">Zip Code *</label>
            <input
              id="zipcode"
              type="text"
              class="form-control"
              formControlName="zipcode"
              placeholder="Enter zip code"
              [class.is-invalid]="isFieldInvalid('zipcode')"
            />
            <small class="error-message" *ngIf="isFieldInvalid('zipcode')">
              {{ getFieldError('zipcode') }}
            </small>
          </div>
        </div>
      </div>

      <!-- Form Actions -->
      <div class="form-actions">
        <button
          type="submit"
          class="p-button p-button-primary"
          [disabled]="companyForm.invalid || isSubmitting"
        >
          <i class="pi pi-plus" *ngIf="!isSubmitting"></i>
          <i class="pi pi-spin pi-spinner" *ngIf="isSubmitting"></i>
          {{ isSubmitting ? 'Creating...' : 'Create Company' }}
        </button>

        <button
          type="button"
          class="p-button p-button-secondary"
        >
          <i class="pi pi-refresh"></i>
          Reset
        </button>
      </div>
      </form>
    </div>
  </div>
</div>
